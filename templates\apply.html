<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请点数 - MiaoMiao AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 500;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 2rem;
        }
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .tab-content {
            padding: 1rem 0;
        }
        .user-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-paper-plane me-2"></i>申请点数与反馈
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">申请额外点数或向管理员反馈问题</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- 用户信息显示 -->
                        {% if current_user %}
                        <div class="user-info">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">
                                        <i class="fas fa-user me-2"></i>{{ current_user.username }}
                                    </h6>
                                    <small class="text-muted">
                                        当前积分：<span class="fw-bold text-primary">{{ current_user.points }}</span>
                                    </small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="/" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-arrow-left me-1"></i>返回首页
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 申请类型选择 -->
                        <ul class="nav nav-tabs" id="applyTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="points-tab" data-bs-toggle="tab" data-bs-target="#points" type="button" role="tab">
                                    <i class="fas fa-coins me-2"></i>申请点数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="feedback-tab" data-bs-toggle="tab" data-bs-target="#feedback" type="button" role="tab">
                                    <i class="fas fa-comment me-2"></i>反馈建议
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="applyTabContent">
                            <!-- 申请点数表单 -->
                            <div class="tab-pane fade show active" id="points" role="tabpanel">
                                <form id="pointsForm">
                                    <div class="mb-3">
                                        <label for="pointsAmount" class="form-label">
                                            <i class="fas fa-coins me-2"></i>申请点数数量
                                        </label>
                                        <select class="form-select" id="pointsAmount" name="points_amount" required>
                                            <option value="">请选择申请的点数数量</option>
                                            <option value="50">50 点数</option>
                                            <option value="100">100 点数</option>
                                            <option value="200">200 点数</option>
                                            <option value="500">500 点数</option>
                                            <option value="custom">自定义数量</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3" id="customPointsDiv" style="display: none;">
                                        <label for="customPoints" class="form-label">自定义点数数量</label>
                                        <input type="number" class="form-control" id="customPoints" name="custom_points" min="1" max="1000" placeholder="请输入1-1000之间的数字">
                                    </div>

                                    <div class="mb-3">
                                        <label for="pointsReason" class="form-label">
                                            <i class="fas fa-edit me-2"></i>申请理由
                                        </label>
                                        <textarea class="form-control" id="pointsReason" name="reason" rows="4" required placeholder="请详细说明您申请点数的理由，例如：用于学习AI绘画、创作项目需要等..."></textarea>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>提交申请
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- 反馈建议表单 -->
                            <div class="tab-pane fade" id="feedback" role="tabpanel">
                                <form id="feedbackForm">
                                    <div class="mb-3">
                                        <label for="feedbackType" class="form-label">
                                            <i class="fas fa-tag me-2"></i>反馈类型
                                        </label>
                                        <select class="form-select" id="feedbackType" name="feedback_type" required>
                                            <option value="">请选择反馈类型</option>
                                            <option value="bug">Bug报告</option>
                                            <option value="feature">功能建议</option>
                                            <option value="improvement">改进建议</option>
                                            <option value="question">使用问题</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="feedbackTitle" class="form-label">
                                            <i class="fas fa-heading me-2"></i>标题
                                        </label>
                                        <input type="text" class="form-control" id="feedbackTitle" name="title" required placeholder="请简要描述问题或建议">
                                    </div>

                                    <div class="mb-3">
                                        <label for="feedbackContent" class="form-label">
                                            <i class="fas fa-comment me-2"></i>详细内容
                                        </label>
                                        <textarea class="form-control" id="feedbackContent" name="content" rows="6" required placeholder="请详细描述您的问题、建议或反馈内容..."></textarea>
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>提交反馈
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 提示信息 -->
                        <div class="alert alert-info mt-4" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong>
                            <ul class="mb-0 mt-2">
                                <li>点数申请将由管理员审核，请耐心等待</li>
                                <li>请如实填写申请理由，虚假申请将被拒绝</li>
                                <li>反馈建议有助于我们改进服务质量</li>
                                <li>您可以随时查看申请状态</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自定义点数输入控制
        document.getElementById('pointsAmount').addEventListener('change', function() {
            const customDiv = document.getElementById('customPointsDiv');
            const customInput = document.getElementById('customPoints');
            
            if (this.value === 'custom') {
                customDiv.style.display = 'block';
                customInput.required = true;
            } else {
                customDiv.style.display = 'none';
                customInput.required = false;
                customInput.value = '';
            }
        });

        // 显示消息
        function showMessage(title, message, isSuccess = true) {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalBody').innerHTML = message;
            
            const modal = new bootstrap.Modal(document.getElementById('messageModal'));
            modal.show();
            
            // 如果成功，3秒后自动关闭并跳转
            if (isSuccess) {
                setTimeout(() => {
                    modal.hide();
                    window.location.href = '/';
                }, 3000);
            }
        }

        // 点数申请表单提交
        document.getElementById('pointsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // 获取点数数量
            let pointsAmount = formData.get('points_amount');
            if (pointsAmount === 'custom') {
                pointsAmount = formData.get('custom_points');
            }
            
            if (!pointsAmount || pointsAmount <= 0) {
                showMessage('错误', '请选择或输入有效的点数数量', false);
                return;
            }
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            
            // 准备提交数据
            const submitData = {
                type: 'points',
                points_amount: parseInt(pointsAmount),
                reason: formData.get('reason')
            };
            
            fetch('/submit_application', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('申请成功', data.message, true);
                    this.reset();
                } else {
                    showMessage('申请失败', data.message, false);
                }
            })
            .catch(error => {
                console.error('申请提交错误:', error);
                showMessage('申请失败', '网络错误，请稍后重试', false);
            })
            .finally(() => {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // 反馈表单提交
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            
            // 准备提交数据
            const submitData = {
                type: 'feedback',
                feedback_type: formData.get('feedback_type'),
                title: formData.get('title'),
                content: formData.get('content')
            };
            
            fetch('/submit_application', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('反馈成功', data.message, true);
                    this.reset();
                } else {
                    showMessage('反馈失败', data.message, false);
                }
            })
            .catch(error => {
                console.error('反馈提交错误:', error);
                showMessage('反馈失败', '网络错误，请稍后重试', false);
            })
            .finally(() => {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    </script>
</body>
</html>
