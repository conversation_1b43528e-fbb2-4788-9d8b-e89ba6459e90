#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户管理API的分页和搜索功能
"""

import requests
import json

def test_users_api():
    base_url = "http://localhost:7799"

    # 创建会话以保持登录状态
    session = requests.Session()

    # 首先尝试登录（如果有管理员账户的话）
    print("尝试登录管理员账户...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }

    try:
        # 使用JSON格式登录
        login_response = session.post(f"{base_url}/login",
                                    json=login_data,
                                    headers={'Content-Type': 'application/json'})
        print(f"登录响应状态码: {login_response.status_code}")

        if login_response.status_code != 200:
            print("登录失败，可能需要先创建管理员账户")
            print("请运行: python init_admin.py")
            return False

    except Exception as e:
        print(f"登录出错: {e}")
        return False

    # 测试基本的用户列表API
    print("\n测试基本用户列表API...")
    try:
        response = session.get(f"{base_url}/admin/users")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据结构: {list(data.keys())}")
            
            if 'success' in data and data['success']:
                print(f"用户数量: {len(data.get('users', []))}")
                if 'pagination' in data:
                    pagination = data['pagination']
                    print(f"分页信息: 当前页={pagination.get('current_page')}, 总页数={pagination.get('total_pages')}, 总用户数={pagination.get('total_users')}")
                else:
                    print("没有分页信息")
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保应用正在运行")
        return False
    except Exception as e:
        print(f"测试出错: {e}")
        return False
    
    # 测试分页参数
    print("\n测试分页参数...")
    try:
        response = session.get(f"{base_url}/admin/users?page=1&per_page=5")
        print(f"分页测试状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    users = data.get('users', [])
                    pagination = data.get('pagination', {})
                    print(f"第1页，每页5条: 实际返回{len(users)}条用户")
                    print(f"分页信息: {pagination}")
                else:
                    print(f"分页测试失败: {data.get('message')}")
            except json.JSONDecodeError:
                print(f"分页测试JSON解析失败，响应内容: {response.text[:200]}")
        else:
            print(f"分页测试HTTP错误: {response.status_code}, 内容: {response.text[:200]}")
    except Exception as e:
        print(f"分页测试出错: {e}")
    
    # 测试搜索参数
    print("\n测试搜索参数...")
    try:
        response = session.get(f"{base_url}/admin/users?search=admin")
        print(f"搜索测试状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    users = data.get('users', [])
                    search_term = data.get('search', '')
                    print(f"搜索'admin': 找到{len(users)}个用户")
                    print(f"搜索关键词: '{search_term}'")
                    for user in users[:3]:  # 只显示前3个
                        print(f"  - {user.get('username')} ({user.get('email')})")
                else:
                    print(f"搜索测试失败: {data.get('message')}")
            except json.JSONDecodeError:
                print(f"搜索测试JSON解析失败，响应内容: {response.text[:200]}")
        else:
            print(f"搜索测试HTTP错误: {response.status_code}, 内容: {response.text[:200]}")
    except Exception as e:
        print(f"搜索测试出错: {e}")
    
    return True

if __name__ == "__main__":
    test_users_api()
