<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 梦羽绘图工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1400px;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 30px;
        }

        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
            padding: 15px 25px;
            margin-right: 10px;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .nav-tabs .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tab-content {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px 15px 0 0;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #495057;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .redemption-stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .redemption-stats-card:hover {
            transform: translateY(-3px);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            color: #667eea;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            color: #764ba2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">
        <i class="fas fa-arrow-left me-2"></i>返回主页
    </a>

    <div class="container-fluid">
        <div class="admin-container">
            <div class="admin-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="mb-0">
                            <i class="fas fa-cogs me-3"></i>管理员面板
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">系统管理与配置中心</p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-shield me-2"></i>
                            <span>{{ current_user.username }}</span>
                        </div>
                        <!-- 隐藏元素存储当前用户名供JavaScript使用 -->
                        <input type="hidden" id="current-username" value="{{ current_user.username }}">
                    </div>
                </div>
            </div>

            <div class="admin-body">
                <ul class="nav nav-tabs" id="adminTabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#usersTab">
                            <i class="fas fa-users me-2"></i>用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#userApprovalTab">
                            <i class="fas fa-user-check me-2"></i>用户审核
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#authorizedUsersTab">
                            <i class="fas fa-user-shield me-2"></i>授权用户
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#statisticsTab">
                            <i class="fas fa-chart-bar me-2"></i>系统统计
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#redemptionTab">
                            <i class="fas fa-ticket-alt me-2"></i>兑换码管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#applicationsTab">
                            <i class="fas fa-paper-plane me-2"></i>申请管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#messagesTab">
                            <i class="fas fa-envelope me-2"></i>站内信管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#historyTab">
                            <i class="fas fa-history me-2"></i>历史记录管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#settingsTab">
                            <i class="fas fa-cog me-2"></i>系统设置
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="adminTabContent">
                    <!-- 用户管理标签页 -->
                    <div class="tab-pane fade show active" id="usersTab">
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-users me-2"></i>用户列表
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- 搜索和分页控制区域 -->
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <span class="input-group-text">
                                                        <i class="fas fa-search"></i>
                                                    </span>
                                                    <input type="text" class="form-control" id="userSearchInput"
                                                           placeholder="搜索用户名或邮箱..." maxlength="100">
                                                    <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="usersPerPageSelect">
                                                    <option value="10">每页 10 条</option>
                                                    <option value="20" selected>每页 20 条</option>
                                                    <option value="50">每页 50 条</option>
                                                    <option value="100">每页 100 条</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <button class="btn btn-outline-primary" id="refreshUsersBtn">
                                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 用户统计信息 -->
                                        <div class="row mb-3">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-0" id="usersStatsInfo">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    <span id="usersStatsText">正在加载用户统计...</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 用户表格 -->
                                        <div id="usersTable">
                                            <div class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2">正在加载用户数据...</p>
                                            </div>
                                        </div>

                                        <!-- 分页导航 -->
                                        <div id="usersPagination" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                                            <div class="pagination-info">
                                                <small class="text-muted" id="paginationInfo"></small>
                                            </div>
                                            <nav aria-label="用户列表分页">
                                                <ul class="pagination pagination-sm mb-0" id="paginationNav">
                                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 积分充值 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-coins me-2"></i>积分充值
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="chargeUsername" class="form-label">用户名</label>
                                            <input type="text" class="form-control" id="chargeUsername" placeholder="输入用户名">
                                        </div>
                                        <div class="mb-3">
                                            <label for="chargePoints" class="form-label">积分数量</label>
                                            <input type="number" class="form-control" id="chargePoints" placeholder="输入积分数量" min="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="chargeReason" class="form-label">充值原因</label>
                                            <input type="text" class="form-control" id="chargeReason" placeholder="充值原因（可选）">
                                        </div>
                                        <button id="chargeBtn" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>充值积分
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户审核标签页 -->
                    <div class="tab-pane fade" id="userApprovalTab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-check me-2"></i>待审核用户
                                </h5>
                                <button id="refreshPendingUsersBtn" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="pendingUsersTable">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载待审核用户...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 授权用户标签页 -->
                    <div class="tab-pane fade" id="authorizedUsersTab">
                        <div class="row">
                            <!-- 授权用户统计 -->
                            <div class="col-12 mb-4">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="stats-card">
                                            <div class="stats-number" id="totalAuthorizedUsers">-</div>
                                            <div class="stats-label">授权用户总数</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stats-card">
                                            <div class="stats-number" id="totalRegularUsers">-</div>
                                            <div class="stats-label">普通用户总数</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stats-card">
                                            <div class="stats-number" id="totalAdminUsers">-</div>
                                            <div class="stats-label">管理员总数</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stats-card">
                                            <div class="stats-number" id="authorizationRate">-%</div>
                                            <div class="stats-label">授权率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 授权用户列表 -->
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-user-shield me-2"></i>授权用户列表
                                        </h5>
                                        <button id="refreshAuthorizedUsersBtn" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-sync-alt me-1"></i>刷新
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="authorizedUsersTable">
                                            <div class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2">正在加载授权用户...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统统计标签页 -->
                    <div class="tab-pane fade" id="statisticsTab">
                        <div id="statisticsContent">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载统计数据...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 兑换码管理标签页 -->
                    <div class="tab-pane fade" id="redemptionTab">
                        <!-- 兑换码管理头部 -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>兑换码管理
                            </h5>
                            <div class="btn-group">
                                <button id="createCodeBtn" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>创建兑换码
                                </button>
                                <button id="batchCreateBtn" class="btn btn-outline-primary">
                                    <i class="fas fa-layer-group me-1"></i>批量创建
                                </button>
                                <button id="cleanupDisabledBtn" class="btn btn-warning">
                                    <i class="fas fa-broom me-1"></i>清理已禁用
                                </button>
                            </div>
                        </div>

                        <!-- 兑换码统计 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-ticket-alt fa-2x mb-2"></i>
                                        <h3 id="totalCodesCount" class="mb-1">-</h3>
                                        <small>总兑换码数</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h3 id="activeCodesCount" class="mb-1">-</h3>
                                        <small>有效兑换码</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <h3 id="usedCodesCount" class="mb-1">-</h3>
                                        <small>已使用</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card redemption-stats-card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-coins fa-2x mb-2"></i>
                                        <h3 id="totalPointsDistributed" class="mb-1">-</h3>
                                        <small>总发放积分</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 兑换码列表 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">兑换码列表</h6>
                            </div>
                            <div class="card-body">
                                <div id="redemptionCodesTable">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载兑换码数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近使用记录 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">最近使用记录</h6>
                            </div>
                            <div class="card-body">
                                <div id="recentRedemptions">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载使用记录...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 申请管理标签页 -->
                    <div class="tab-pane fade" id="applicationsTab">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-paper-plane me-2"></i>申请管理
                            </h5>
                            <div class="btn-group">
                                <button id="refreshApplicationsBtn" class="btn btn-outline-primary">
                                    <i class="fas fa-sync me-1"></i>刷新
                                </button>
                                <button id="exportApplicationsBtn" class="btn btn-outline-success">
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                            </div>
                        </div>

                        <!-- 申请统计 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">
                                            <i class="fas fa-clock me-2"></i>待处理
                                        </h5>
                                        <h3 class="text-warning" id="pendingApplicationsCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">
                                            <i class="fas fa-check me-2"></i>已通过
                                        </h5>
                                        <h3 class="text-success" id="approvedApplicationsCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">
                                            <i class="fas fa-times me-2"></i>已拒绝
                                        </h5>
                                        <h3 class="text-danger" id="rejectedApplicationsCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">
                                            <i class="fas fa-list me-2"></i>总计
                                        </h5>
                                        <h3 class="text-info" id="totalApplicationsCount">-</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 申请过滤器 -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row align-items-end">
                                    <div class="col-md-3">
                                        <label for="applicationTypeFilter" class="form-label">申请类型</label>
                                        <select class="form-select" id="applicationTypeFilter">
                                            <option value="">全部类型</option>
                                            <option value="points">点数申请</option>
                                            <option value="feedback">反馈建议</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="applicationStatusFilter" class="form-label">处理状态</label>
                                        <select class="form-select" id="applicationStatusFilter">
                                            <option value="">全部状态</option>
                                            <option value="pending">待处理</option>
                                            <option value="approved">已通过</option>
                                            <option value="rejected">已拒绝</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="applicationUserFilter" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="applicationUserFilter" placeholder="输入用户名筛选">
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100" onclick="filterApplications()">
                                            <i class="fas fa-filter me-1"></i>筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 申请列表 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>申请列表
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="applicationsTable">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载申请数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 站内信管理标签页 -->
                    <div class="tab-pane fade" id="messagesTab">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>站内信管理
                            </h5>
                            <div class="btn-group">
                                <button id="refreshMessageStatsBtn" class="btn btn-outline-primary">
                                    <i class="fas fa-sync me-1"></i>刷新统计
                                </button>
                            </div>
                        </div>

                        <!-- 站内信统计 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary">总消息数</h5>
                                        <h3 id="totalMessagesCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">已读消息</h5>
                                        <h3 id="readMessagesCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">未读消息</h5>
                                        <h3 id="unreadMessagesCount">-</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">管理员消息</h5>
                                        <h3 id="adminMessagesCount">-</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 发送站内信 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-paper-plane me-2"></i>发送站内信
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="sendMessageForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="messageRecipient" class="form-label">收件人</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="messageRecipient"
                                                           placeholder="输入用户名或输入 'all' 发送给所有用户">
                                                    <button type="button" class="btn btn-outline-secondary" id="sendToAllBtn">
                                                        发送给所有用户
                                                    </button>
                                                </div>
                                                <div class="form-text">输入具体用户名或选择发送给所有用户</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="messageTitle" class="form-label">消息标题</label>
                                                <input type="text" class="form-control" id="messageTitle"
                                                       placeholder="输入消息标题" maxlength="100">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="messageContent" class="form-label">消息内容</label>
                                        <textarea class="form-control" id="messageContent" rows="4"
                                                  placeholder="输入消息内容" maxlength="1000"></textarea>
                                        <div class="form-text">最多1000个字符</div>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-secondary me-2" id="clearMessageFormBtn">
                                            <i class="fas fa-eraser me-1"></i>清空
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-1"></i>发送消息
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 发送结果提示 -->
                        <div id="messageAlert" class="alert d-none" role="alert"></div>
                    </div>

                    <!-- 历史记录管理标签页 -->
                    <div class="tab-pane fade" id="historyTab">
                        <div class="row">
                            <!-- 历史记录统计 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-line me-2"></i>历史记录统计
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row" id="historyStats">
                                            <div class="col-md-3 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="totalUsers">-</div>
                                                    <div class="stat-label">总用户数</div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="usersWithHistory">-</div>
                                                    <div class="stat-label">有历史记录的用户</div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="totalRecords">-</div>
                                                    <div class="stat-label">总记录数</div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="avgRecords">-</div>
                                                    <div class="stat-label">平均记录数/用户</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-4 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="records7Days">-</div>
                                                    <div class="stat-label">最近7天记录</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="records30Days">-</div>
                                                    <div class="stat-label">最近30天记录</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <div class="stat-card">
                                                    <div class="stat-value" id="maxRecordsPerUser">-</div>
                                                    <div class="stat-label">单用户最大记录数</div>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary" onclick="loadHistoryStatistics()">
                                            <i class="fas fa-sync-alt me-2"></i>刷新统计
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 历史记录清理 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-broom me-2"></i>历史记录清理
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            清理历史记录可以减少数据文件大小，提高系统性能。建议定期执行清理操作。
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="maxRecords" class="form-label">每用户最大保留记录数</label>
                                                    <input type="number" class="form-control" id="maxRecords" value="50" min="1" max="1000">
                                                    <div class="form-text">建议值：50-100条</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="daysToKeep" class="form-label">保留天数</label>
                                                    <input type="number" class="form-control" id="daysToKeep" value="30" min="1" max="365">
                                                    <div class="form-text">超过此天数的记录将被删除</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="dryRun" checked>
                                                <label class="form-check-label" for="dryRun">
                                                    模拟运行（不实际删除数据，仅显示统计）
                                                </label>
                                            </div>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button class="btn btn-warning" onclick="cleanupHistory()">
                                                <i class="fas fa-broom me-2"></i>执行清理
                                            </button>
                                            <button class="btn btn-info" onclick="previewCleanup()">
                                                <i class="fas fa-eye me-2"></i>预览清理效果
                                            </button>
                                        </div>

                                        <!-- 清理结果显示 -->
                                        <div id="cleanupResult" class="mt-3" style="display: none;">
                                            <div class="alert alert-success">
                                                <h6><i class="fas fa-check-circle me-2"></i>清理完成</h6>
                                                <div id="cleanupStats"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置标签页 -->
                    <div class="tab-pane fade" id="settingsTab">
                        <div class="row">
                            <!-- 代理设置 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-network-wired me-2"></i>代理池设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="globalProxyApiUrl" class="form-label">全局代理池API地址</label>
                                            <input type="url" class="form-control" id="globalProxyApiUrl"
                                                   placeholder="输入代理池API地址，例如：http://example.com/api/proxy">
                                            <div class="form-text">
                                                配置后，所有用户的请求都将通过此代理池进行处理。留空则使用直连。
                                            </div>
                                        </div>
                                        <button id="saveProxySettingsBtn" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>保存代理设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 积分设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-coins me-2"></i>积分消耗设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="imageGenerationCost" class="form-label">图像生成消耗积分</label>
                                            <input type="number" class="form-control" id="imageGenerationCost" min="1" value="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="videoGenerationCost" class="form-label">视频生成消耗积分</label>
                                            <input type="number" class="form-control" id="videoGenerationCost" min="1" value="5">
                                        </div>
                                        <button id="savePointsSettingsBtn" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>保存积分设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 签到积分设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-calendar-check me-2"></i>签到积分设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="dailyCheckinBonus" class="form-label">每日签到奖励积分</label>
                                            <input type="number" class="form-control" id="dailyCheckinBonus" min="1" value="100">
                                            <div class="form-text">
                                                设置用户每天签到可以获得的积分数量
                                            </div>
                                        </div>
                                        <button id="saveCheckinSettingsBtn" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>保存签到设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 注册设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-plus me-2"></i>用户注册设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="requireRegistrationApproval">
                                            <label class="form-check-label" for="requireRegistrationApproval">
                                                新用户注册需要管理员审核
                                            </label>
                                            <div class="form-text">开启后，新注册的用户需要等待管理员审核通过才能使用系统</div>
                                        </div>
                                        <button id="saveRegistrationSettingsBtn" class="btn btn-primary mt-3">
                                            <i class="fas fa-save me-1"></i>保存注册设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 画廊图片代理设置 -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-images me-2"></i>画廊图片代理设置
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="galleryImageProxyEnabled">
                                            <label class="form-check-label" for="galleryImageProxyEnabled">
                                                启用画廊图片代理模式
                                            </label>
                                            <div class="form-text">开启后，画廊中的图片将通过 https://i0.wp.com/ 代理显示，可以提高图片加载速度</div>
                                        </div>
                                        <button id="saveGalleryProxySettingsBtn" class="btn btn-primary mt-3">
                                            <i class="fas fa-save me-1"></i>保存代理设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建兑换码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="codeType" class="form-label">兑换码类型</label>
                        <select class="form-select" id="codeType">
                            <option value="one_time">一次性兑换码</option>
                            <option value="activity">活动兑换码</option>
                        </select>
                        <div class="form-text">
                            <small>一次性兑换码：使用一次后失效；活动兑换码：每个用户只能使用一次，但可以被多个用户使用</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="codePoints" class="form-label">积分数量</label>
                        <input type="number" class="form-control" id="codePoints" min="1" value="10">
                    </div>
                    <div class="mb-3">
                        <label for="codeExpireDays" class="form-label">有效期（天）</label>
                        <input type="number" class="form-control" id="codeExpireDays" min="1" value="30">
                    </div>
                    <div class="mb-3">
                        <label for="codeDescription" class="form-label">描述（可选）</label>
                        <input type="text" class="form-control" id="codeDescription" placeholder="兑换码描述">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmCreateCodeBtn">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量创建兑换码模态框 -->
    <div class="modal fade" id="batchCreateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量创建兑换码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchCodeType" class="form-label">兑换码类型</label>
                                <select class="form-select" id="batchCodeType">
                                    <option value="one_time">一次性兑换码</option>
                                    <option value="activity">活动兑换码</option>
                                </select>
                                <div class="form-text">
                                    <small>一次性兑换码：使用一次后失效；活动兑换码：每个用户只能使用一次，但可以被多个用户使用</small>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="batchCodeCount" class="form-label">创建数量</label>
                                <input type="number" class="form-control" id="batchCodeCount" min="1" max="100" value="10">
                                <div class="form-text">最多一次创建100个</div>
                            </div>
                            <div class="mb-3">
                                <label for="batchCodePoints" class="form-label">每个积分数量</label>
                                <input type="number" class="form-control" id="batchCodePoints" min="1" value="10">
                            </div>
                            <div class="mb-3">
                                <label for="batchCodeExpireDays" class="form-label">有效期（天）</label>
                                <input type="number" class="form-control" id="batchCodeExpireDays" min="1" value="30">
                            </div>
                            <div class="mb-3">
                                <label for="batchCodeDescription" class="form-label">描述（可选）</label>
                                <input type="text" class="form-control" id="batchCodeDescription" placeholder="批量兑换码描述">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">预览</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>类型：</strong><span id="previewType">一次性兑换码</span></p>
                                    <p><strong>数量：</strong><span id="previewCount">10</span> 个</p>
                                    <p><strong>每个积分：</strong><span id="previewPoints">10</span> 点</p>
                                    <p><strong>过期时间：</strong><span id="previewExpireDate">-</span></p>
                                    <p><strong>总积分：</strong><span id="previewTotalPoints">100</span> 点</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmBatchCreateBtn">批量创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后自动加载管理员数据
            loadAdminData();

            // 初始化兑换码管理功能
            initRedemptionCodeManagement();

            // ==================== 兑换码管理相关函数 ====================

            // 显示兑换码统计
            window.displayRedemptionStats = function(stats) {
                // 更新统计卡片
                document.getElementById('totalCodesCount').textContent = stats.total_codes || 0;
                document.getElementById('activeCodesCount').textContent = stats.active_codes || 0;
                document.getElementById('usedCodesCount').textContent = stats.total_codes_used || 0;
                document.getElementById('totalPointsDistributed').textContent = stats.total_points_distributed || 0;
            };

            // 显示最近使用记录
            window.displayRecentRedemptions = function(records) {
                const container = document.getElementById('recentRedemptions');

                if (records.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无使用记录</p></div>';
                    return;
                }

                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>用户</th><th>兑换码</th><th>积分</th><th>类型</th><th>使用时间</th></tr></thead><tbody>';
                records.slice(0, 20).forEach((record) => {
                    const date = new Date(record.used_at).toLocaleString();
                    const typeText = record.code_type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = record.code_type === 'one_time' ? 'bg-primary' : 'bg-success';

                    html += `
                        <tr>
                            <td><strong>${record.username}</strong></td>
                            <td><code>${record.code}</code></td>
                            <td><span class="text-success fw-bold">+${record.points}</span></td>
                            <td><span class="badge ${typeClass}">${typeText}</span></td>
                            <td><small class="text-muted">${date}</small></td>
                        </tr>
                    `;
                });
                html += '</tbody></table></div>';

                container.innerHTML = html;
            };

            // 显示兑换码列表
            window.displayRedemptionCodesTable = function(codes) {
                const container = document.getElementById('redemptionCodesTable');

                if (codes.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无兑换码</p></div>';
                    return;
                }

                let html = `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="selectAllCodes()">
                                <i class="fas fa-check-square me-1"></i>全选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="clearSelection()">
                                <i class="fas fa-square me-1"></i>清空
                            </button>
                        </div>
                        <div>
                            <button class="btn btn-outline-info btn-sm" onclick="batchCopyCodes()">
                                <i class="fas fa-copy me-1"></i>批量复制
                            </button>
                            <button class="btn btn-outline-primary btn-sm ms-1" onclick="batchExportCodes()">
                                <i class="fas fa-download me-1"></i>批量导出
                            </button>
                            <button class="btn btn-outline-success btn-sm ms-1" onclick="batchActivateCodes()">
                                <i class="fas fa-play me-1"></i>批量启用
                            </button>
                            <button class="btn btn-outline-warning btn-sm ms-1" onclick="batchDeactivateCodes()">
                                <i class="fas fa-pause me-1"></i>批量禁用
                            </button>
                            <button class="btn btn-outline-danger btn-sm ms-1" onclick="batchDeleteCodes()">
                                <i class="fas fa-trash me-1"></i>批量删除
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="50"><input type="checkbox" id="selectAllCheckbox" onchange="toggleAllCodes(this)"></th>
                                    <th>兑换码</th>
                                    <th>积分</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>使用次数</th>
                                    <th>过期时间</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                codes.forEach((code) => {
                    const expireDate = new Date(code.expire_at);
                    const isExpired = new Date() > expireDate;
                    const createDate = new Date(code.created_at);

                    // 状态判断
                    let statusText, statusClass, statusIcon;
                    if (isExpired) {
                        statusText = '已过期';
                        statusClass = 'bg-danger';
                        statusIcon = 'fas fa-times-circle';
                    } else if (!code.is_active) {
                        statusText = '已禁用';
                        statusClass = 'bg-warning';
                        statusIcon = 'fas fa-pause-circle';
                    } else if (code.type === 'one_time' && code.used_count > 0) {
                        statusText = '已使用';
                        statusClass = 'bg-secondary';
                        statusIcon = 'fas fa-check-circle';
                    } else {
                        statusText = '有效';
                        statusClass = 'bg-success';
                        statusIcon = 'fas fa-check-circle';
                    }

                    const typeText = code.type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = code.type === 'one_time' ? 'bg-primary' : 'bg-info';

                    html += `
                        <tr>
                            <td><input type="checkbox" class="code-checkbox" value="${code.code}"></td>
                            <td><code>${code.code}</code><br><small class="text-muted">${createDate.toLocaleDateString()}</small></td>
                            <td><strong>${code.points}</strong></td>
                            <td><span class="badge ${typeClass}">${typeText}</span></td>
                            <td><span class="badge ${statusClass}"><i class="${statusIcon} me-1"></i>${statusText}</span></td>
                            <td>${code.used_count}</td>
                            <td class="${isExpired ? 'text-danger' : ''}">${expireDate.toLocaleDateString()}</td>
                            <td><small title="${code.description || '无描述'}">${code.description ? (code.description.length > 15 ? code.description.substring(0, 15) + '...' : code.description) : '无描述'}</small></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    ${!isExpired ? `
                                        <button class="btn btn-outline-${code.is_active ? 'warning' : 'success'}" onclick="toggleRedemptionCode('${code.code}', '${code.is_active ? 'deactivate' : 'activate'}')" title="${code.is_active ? '禁用' : '启用'}">
                                            <i class="fas fa-${code.is_active ? 'pause' : 'play'}"></i>
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-outline-primary" onclick="copyCodeToClipboard('${code.code}')" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteRedemptionCode('${code.code}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            };

            // 加载兑换码数据
            window.loadRedemptionData = function() {
                // 加载兑换码统计
                fetch('/admin/redemption_statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionStats(data.statistics);
                        } else {
                            console.error('加载统计数据失败:', data.message);
                            // 显示错误状态
                            document.getElementById('totalCodesCount').textContent = '-';
                            document.getElementById('activeCodesCount').textContent = '-';
                            document.getElementById('usedCodesCount').textContent = '-';
                            document.getElementById('totalPointsDistributed').textContent = '-';
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                        // 显示错误状态
                        document.getElementById('totalCodesCount').textContent = '-';
                        document.getElementById('activeCodesCount').textContent = '-';
                        document.getElementById('usedCodesCount').textContent = '-';
                        document.getElementById('totalPointsDistributed').textContent = '-';
                    });

                // 加载兑换码列表
                fetch('/admin/redemption_codes')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionCodesTable(data.codes);
                        } else {
                            const container = document.getElementById('redemptionCodesTable');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('redemptionCodesTable');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载兑换码失败: ${error}</div>`;
                        }
                    });

                // 加载使用记录
                fetch('/admin/redemption_usage_records')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRecentRedemptions(data.records);
                        } else {
                            const container = document.getElementById('recentRedemptions');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('recentRedemptions');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载使用记录失败: ${error}</div>`;
                        }
                    });
            };









            // 积分充值按钮事件
            const chargeBtn = document.getElementById('chargeBtn');
            if (chargeBtn) {
                chargeBtn.addEventListener('click', function() {
                    const username = document.getElementById('chargeUsername').value.trim();
                    const points = parseInt(document.getElementById('chargePoints').value);
                    const reason = document.getElementById('chargeReason').value.trim() || '管理员充值';

                    if (!username || !points || points <= 0) {
                        alert('请填写正确的用户名和积分数');
                        return;
                    }

                    fetch('/admin/add_points', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            points: points,
                            reason: reason
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        if (data.success) {
                            // 清空表单
                            document.getElementById('chargeUsername').value = '';
                            document.getElementById('chargePoints').value = '';
                            document.getElementById('chargeReason').value = '';
                            // 重新加载用户数据
                            loadUsersData();
                        }
                    })
                    .catch(error => {
                        alert('操作失败: ' + error);
                    });
                });
            }

            // 保存代理设置
            const saveProxySettingsBtn = document.getElementById('saveProxySettingsBtn');
            if (saveProxySettingsBtn) {
                saveProxySettingsBtn.addEventListener('click', function() {
                    const globalProxyApiUrl = document.getElementById('globalProxyApiUrl').value.trim();

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            global_proxy_api_url: globalProxyApiUrl
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存积分设置
            const savePointsSettingsBtn = document.getElementById('savePointsSettingsBtn');
            if (savePointsSettingsBtn) {
                savePointsSettingsBtn.addEventListener('click', function() {
                    const imageGenerationCost = parseInt(document.getElementById('imageGenerationCost').value);
                    const videoGenerationCost = parseInt(document.getElementById('videoGenerationCost').value);

                    if (imageGenerationCost < 1 || videoGenerationCost < 1) {
                        alert('积分消耗必须大于0');
                        return;
                    }

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_generation_cost: imageGenerationCost,
                            video_generation_cost: videoGenerationCost
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存签到设置
            const saveCheckinSettingsBtn = document.getElementById('saveCheckinSettingsBtn');
            if (saveCheckinSettingsBtn) {
                saveCheckinSettingsBtn.addEventListener('click', function() {
                    const dailyCheckinBonus = parseInt(document.getElementById('dailyCheckinBonus').value);

                    if (dailyCheckinBonus < 1) {
                        alert('签到积分必须大于0');
                        return;
                    }

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            daily_bonus: dailyCheckinBonus
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存注册设置
            const saveRegistrationSettingsBtn = document.getElementById('saveRegistrationSettingsBtn');
            if (saveRegistrationSettingsBtn) {
                saveRegistrationSettingsBtn.addEventListener('click', function() {
                    const requireRegistrationApproval = document.getElementById('requireRegistrationApproval').checked;

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            require_registration_approval: requireRegistrationApproval
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 保存画廊图片代理设置
            const saveGalleryProxySettingsBtn = document.getElementById('saveGalleryProxySettingsBtn');
            if (saveGalleryProxySettingsBtn) {
                saveGalleryProxySettingsBtn.addEventListener('click', function() {
                    const galleryImageProxyEnabled = document.getElementById('galleryImageProxyEnabled').checked;

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            gallery_image_proxy_enabled: galleryImageProxyEnabled
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('画廊图片代理设置保存成功！');
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert('保存失败: ' + error);
                    });
                });
            }

            // 刷新待审核用户按钮
            const refreshPendingUsersBtn = document.getElementById('refreshPendingUsersBtn');
            if (refreshPendingUsersBtn) {
                refreshPendingUsersBtn.addEventListener('click', function() {
                    fetch('/admin/pending_users')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayPendingUsersTable(data.pending_users);
                            } else {
                                alert('刷新失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            alert('刷新失败: ' + error);
                        });
                });
            }

            // 监听兑换码管理标签页激活事件（仅用于加载数据，不重复初始化）
            const redemptionTabTrigger = document.querySelector('a[href="#redemptionTab"]');
            if (redemptionTabTrigger) {
                redemptionTabTrigger.addEventListener('shown.bs.tab', function() {
                    console.log('兑换码管理标签页已激活，加载数据');
                    if (typeof window.loadRedemptionData === 'function') {
                        window.loadRedemptionData();
                    }
                });
            }

            // ==================== 用户管理相关事件监听器 ====================

            // 搜索输入框事件
            const userSearchInput = document.getElementById('userSearchInput');
            if (userSearchInput) {
                userSearchInput.addEventListener('input', function() {
                    clearTimeout(usersSearchTimeout);
                    usersSearchTimeout = setTimeout(() => {
                        currentUsersPage = 1; // 搜索时重置到第一页
                        loadUsersData(1, null, this.value);
                    }, 500); // 500ms 防抖
                });

                userSearchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        clearTimeout(usersSearchTimeout);
                        currentUsersPage = 1;
                        loadUsersData(1, null, this.value);
                    }
                });
            }

            // 清除搜索按钮
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', function() {
                    userSearchInput.value = '';
                    currentUsersPage = 1;
                    loadUsersData(1, null, '');
                });
            }

            // 每页显示数量选择器
            const usersPerPageSelect = document.getElementById('usersPerPageSelect');
            if (usersPerPageSelect) {
                usersPerPageSelect.addEventListener('change', function() {
                    currentUsersPage = 1; // 改变每页数量时重置到第一页
                    loadUsersData(1, parseInt(this.value), null);
                });
            }

            // 刷新用户列表按钮
            const refreshUsersBtn = document.getElementById('refreshUsersBtn');
            if (refreshUsersBtn) {
                refreshUsersBtn.addEventListener('click', function() {
                    loadUsersData(currentUsersPage, currentUsersPerPage, currentUsersSearch);
                });
            }
        });

        // 用户列表分页和搜索状态
        let currentUsersPage = 1;
        let currentUsersPerPage = 20;
        let currentUsersSearch = '';
        let usersSearchTimeout = null;

        // 加载管理员数据 - 全局函数
        function loadAdminData() {
            // 加载用户列表
            loadUsersData();

            // 加载统计信息
            fetch('/admin/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayStatistics(data.statistics);
                    } else {
                        document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">加载统计数据失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">加载统计数据失败: ${error}</div>`;
                });

            // 加载待审核用户
            fetch('/admin/pending_users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPendingUsersTable(data.pending_users);
                    } else {
                        document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${error}</div>`;
                });

            // 加载系统设置
            fetch('/admin/settings')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('globalProxyApiUrl').value = data.settings.global_proxy_api_url || '';
                        document.getElementById('imageGenerationCost').value = data.settings.image_generation_cost || 1;
                        document.getElementById('videoGenerationCost').value = data.settings.video_generation_cost || 5;
                        document.getElementById('dailyCheckinBonus').value = data.settings.daily_bonus || 100;
                        document.getElementById('requireRegistrationApproval').checked = data.settings.require_registration_approval || false;
                        document.getElementById('galleryImageProxyEnabled').checked = data.settings.gallery_image_proxy_enabled || false;
                    }
                })
                .catch(error => {
                    console.error('加载系统设置失败:', error);
                });

            // 加载授权用户数据
            loadAuthorizedUsersData();

            // 加载兑换码数据
            if (typeof window.loadRedemptionData === 'function') {
                window.loadRedemptionData();
            } else {
                console.warn('loadRedemptionData function not yet defined, will retry...');
                // 延迟执行，确保函数已定义
                setTimeout(function() {
                    if (typeof window.loadRedemptionData === 'function') {
                        window.loadRedemptionData();
                    } else {
                        console.error('loadRedemptionData function still not defined');
                    }
                }, 100);
            }
        }

        // 加载用户数据 - 支持分页和搜索
        function loadUsersData(page = 1, perPage = null, search = null) {
            // 更新当前状态
            if (page) currentUsersPage = page;
            if (perPage !== null) currentUsersPerPage = perPage;
            if (search !== null) currentUsersSearch = search;

            // 构建请求URL
            const params = new URLSearchParams({
                page: currentUsersPage,
                per_page: currentUsersPerPage
            });

            if (currentUsersSearch.trim()) {
                params.append('search', currentUsersSearch.trim());
            }

            // 显示加载状态
            document.getElementById('usersTable').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载用户数据...</p>
                </div>
            `;

            fetch(`/admin/users?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayUsersTable(data.users, data.pagination, data.search);
                        updateUsersStats(data.pagination);
                        updateUsersPagination(data.pagination);
                    } else {
                        document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">加载用户数据失败: ${data.message}</div>`;
                        document.getElementById('usersPagination').style.display = 'none';
                    }
                })
                .catch(error => {
                    document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">加载用户数据失败: ${error}</div>`;
                    document.getElementById('usersPagination').style.display = 'none';
                });
        }

        // 更新用户统计信息
        function updateUsersStats(pagination) {
            const { current_page, per_page, total_users, total_pages } = pagination;
            const start = (current_page - 1) * per_page + 1;
            const end = Math.min(current_page * per_page, total_users);

            let statsText = `共 ${total_users} 个用户`;
            if (total_users > 0) {
                statsText += `，当前显示第 ${start}-${end} 个`;
            }
            if (currentUsersSearch.trim()) {
                statsText += `，搜索关键词："${currentUsersSearch.trim()}"`;
            }

            document.getElementById('usersStatsText').textContent = statsText;
        }

        // 更新分页导航
        function updateUsersPagination(pagination) {
            const { current_page, total_pages, has_prev, has_next, prev_page, next_page } = pagination;

            if (total_pages <= 1) {
                document.getElementById('usersPagination').style.display = 'none';
                return;
            }

            document.getElementById('usersPagination').style.display = 'flex';

            // 更新分页信息
            document.getElementById('paginationInfo').textContent = `第 ${current_page} 页，共 ${total_pages} 页`;

            // 生成分页按钮
            let paginationHtml = '';

            // 上一页按钮
            paginationHtml += `
                <li class="page-item ${!has_prev ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="${has_prev ? `loadUsersData(${prev_page})` : 'return false'}"
                       ${!has_prev ? 'tabindex="-1" aria-disabled="true"' : ''}>
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(total_pages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadUsersData(1)">1</a>
                    </li>
                `;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <li class="page-item ${i === current_page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadUsersData(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < total_pages) {
                if (endPage < total_pages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadUsersData(${total_pages})">${total_pages}</a>
                    </li>
                `;
            }

            // 下一页按钮
            paginationHtml += `
                <li class="page-item ${!has_next ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="${has_next ? `loadUsersData(${next_page})` : 'return false'}"
                       ${!has_next ? 'tabindex="-1" aria-disabled="true"' : ''}>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            document.getElementById('paginationNav').innerHTML = paginationHtml;
        }

        // 显示用户表格 - 全局函数
        function displayUsersTable(users, pagination = null, search = null) {
            // 检查是否有用户数据
            if (!users || users.length === 0) {
                let emptyMessage = '暂无用户数据';
                if (search && search.trim()) {
                    emptyMessage = `未找到包含"${search.trim()}"的用户`;
                }
                document.getElementById('usersTable').innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">${emptyMessage}</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>积分</th>
                                <th>生成次数</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>角色</th>
                                <th>授权状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            users.forEach(user => {
                // 获取当前登录的管理员用户名（从全局变量或其他方式获取）
                const currentUsername = getCurrentUsername(); // 需要实现这个函数
                const isCurrentUser = user.username === currentUsername;
                const isAdmin = user.is_admin;
                const isAuthorized = user.is_authorized;

                // 角色显示
                let roleDisplay = '';
                if (isAdmin) {
                    roleDisplay = '<span class="badge bg-danger">管理员</span>';
                } else if (isAuthorized) {
                    roleDisplay = '<span class="badge bg-success">授权用户</span>';
                } else {
                    roleDisplay = '<span class="badge bg-secondary">普通用户</span>';
                }

                // 授权状态显示
                let authStatusDisplay = '';
                if (isAdmin) {
                    authStatusDisplay = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>默认授权</span>';
                } else if (isAuthorized) {
                    const authorizedDate = user.authorized_at ? new Date(user.authorized_at).toLocaleDateString() : '';
                    const authorizedBy = user.authorized_by || '';
                    authStatusDisplay = `<span class="text-success"><i class="fas fa-check-circle me-1"></i>已授权</span>
                                       <br><small class="text-muted">由 ${authorizedBy} 于 ${authorizedDate} 授权</small>`;
                } else {
                    authStatusDisplay = '<span class="text-warning"><i class="fas fa-times-circle me-1"></i>未授权</span>';
                }

                html += `
                    <tr>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email || '-'}</td>
                        <td>${user.points}</td>
                        <td>${user.total_generated}</td>
                        <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                        <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : '-'}</td>
                        <td>${roleDisplay}</td>
                        <td>${authStatusDisplay}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                ${!isCurrentUser ? `
                                    <button type="button" class="btn btn-warning btn-sm"
                                            onclick="resetUserPassword('${user.username}')"
                                            title="重置密码">
                                        <i class="fas fa-key"></i>
                                    </button>
                                ` : ''}
                                ${!isCurrentUser && !isAdmin ? `
                                    ${isAuthorized ? `
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="setUserAuthorized('${user.username}', false)"
                                                title="取消授权">
                                            <i class="fas fa-user-times"></i>
                                        </button>
                                    ` : `
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="setUserAuthorized('${user.username}', true)"
                                                title="设为授权用户">
                                            <i class="fas fa-user-check"></i>
                                        </button>
                                    `}
                                    <button type="button" class="btn btn-danger btn-sm"
                                            onclick="deleteUser('${user.username}')"
                                            title="删除用户">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                                ${isCurrentUser ? '<span class="text-muted small">当前用户</span>' : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('usersTable').innerHTML = html;
        }

        // 显示统计信息 - 全局函数
        function displayStatistics(stats) {
            const systemStats = stats.system_stats || {};
            const html = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">${stats.total_users || 0}</div>
                            <div class="stats-label">总用户数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">${systemStats.total_generations || 0}</div>
                            <div class="stats-label">总生成次数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">${systemStats.total_points_consumed || 0}</div>
                            <div class="stats-label">总消耗积分</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">${systemStats.total_points_issued || 0}</div>
                            <div class="stats-label">总发放积分</div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-end">
                            <button class="btn btn-outline-primary btn-sm" onclick="recalculateStatistics()">
                                <i class="fas fa-sync-alt me-1"></i>重新计算统计
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">最近活动</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>用户</th>
                                                <th>操作</th>
                                                <th>时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${stats.recent_activities.map(activity => `
                                                <tr>
                                                    <td>${activity.username}</td>
                                                    <td>${activity.description}</td>
                                                    <td>${new Date(activity.timestamp).toLocaleString()}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('statisticsContent').innerHTML = html;
        }

        // 重新计算统计数据 - 全局函数
        function recalculateStatistics() {
            if (!confirm('确定要重新计算统计数据吗？这可能需要一些时间。')) {
                return;
            }

            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>计算中...';

            fetch('/admin/recalculate_statistics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('统计数据重新计算完成！');
                    // 重新加载统计数据
                    loadAdminData();
                } else {
                    alert('重新计算失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('重新计算失败: ' + error);
            })
            .finally(() => {
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // 显示待审核用户表格 - 全局函数
        function displayPendingUsersTable(pendingUsers) {
            if (pendingUsers.length === 0) {
                document.getElementById('pendingUsersTable').innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无待审核用户</p></div>';
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>申请理由</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            pendingUsers.forEach(user => {
                const reason = user.registration_reason || '-';
                const reasonDisplay = reason.length > 50 ? reason.substring(0, 50) + '...' : reason;
                html += `
                    <tr>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email || '-'}</td>
                        <td>
                            <span title="${reason}" data-bs-toggle="tooltip">${reasonDisplay}</span>
                        </td>
                        <td>${new Date(user.created_at).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-success btn-sm me-2" onclick="approveUser('${user.username}')">
                                <i class="fas fa-check me-1"></i>通过
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="rejectUser('${user.username}')">
                                <i class="fas fa-times me-1"></i>拒绝
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('pendingUsersTable').innerHTML = html;

            // 初始化 Bootstrap tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }



        // 审核通过用户
        function approveUser(username) {
            if (confirm(`确定要通过用户 "${username}" 的审核吗？`)) {
                fetch('/admin/approve_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username
                    })
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        // 重新加载待审核用户列表
                        fetch('/admin/pending_users')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displayPendingUsersTable(data.pending_users);
                                }
                            });
                    }
                })
                .catch(error => {
                    alert('操作失败: ' + error);
                });
            }
        }

        // 拒绝用户
        function rejectUser(username) {
            const reason = prompt(`请输入拒绝用户 "${username}" 的原因（可选）:`);
            if (reason !== null) { // 用户没有取消
                fetch('/admin/reject_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        reason: reason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        // 重新加载待审核用户列表
                        fetch('/admin/pending_users')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displayPendingUsersTable(data.pending_users);
                                }
                            });
                    }
                })
                .catch(error => {
                    alert('操作失败: ' + error);
                });
            }
        }

        // 获取当前用户名
        function getCurrentUsername() {
            // 从页面头部的用户信息区域获取
            const userSpan = document.querySelector('.d-flex.align-items-center span');
            if (userSpan) {
                return userSpan.textContent.trim();
            }

            // 备用方法：从模板变量获取（通过隐藏元素）
            const hiddenUsername = document.getElementById('current-username');
            if (hiddenUsername) {
                return hiddenUsername.value || hiddenUsername.textContent.trim();
            }

            // 最后的备用方法：从全局变量获取（如果有的话）
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.username) {
                return currentUser.username;
            }
            return null;
        }

        // 重置用户密码
        function resetUserPassword(username) {
            const newPassword = prompt(`请输入用户 "${username}" 的新密码（至少6个字符）:`);
            if (newPassword !== null && newPassword.trim() !== '') {
                if (newPassword.length < 6) {
                    alert('密码至少需要6个字符');
                    return;
                }

                if (confirm(`确定要重置用户 "${username}" 的密码吗？`)) {
                    fetch('/admin/reset_user_password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username,
                            new_password: newPassword
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        if (data.success) {
                            // 重新加载用户列表
                            loadUsersData();
                        }
                    })
                    .catch(error => {
                        alert('操作失败: ' + error);
                    });
                }
            }
        }

        // 删除用户
        function deleteUser(username) {
            if (confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销！`)) {
                if (confirm(`再次确认：您真的要删除用户 "${username}" 吗？这将永久删除该用户的所有数据！`)) {
                    fetch('/admin/delete_user', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        if (data.success) {
                            // 重新加载用户列表
                            loadUsersData();
                        }
                    })
                    .catch(error => {
                        alert('操作失败: ' + error);
                    });
                }
            }
        }

        // 兑换码管理相关函数
        function initRedemptionCodeManagement() {
            // 防止重复初始化
            if (window.redemptionCodeManagementInitialized) {
                return;
            }
            window.redemptionCodeManagementInitialized = true;

            // 创建兑换码按钮
            const createCodeBtn = document.getElementById('createCodeBtn');
            const createCodeModal = new bootstrap.Modal(document.getElementById('createCodeModal'));

            if (createCodeBtn) {
                createCodeBtn.addEventListener('click', function() {
                    createCodeModal.show();
                });
            }

            // 批量创建按钮
            const batchCreateBtn = document.getElementById('batchCreateBtn');
            const batchCreateModal = new bootstrap.Modal(document.getElementById('batchCreateModal'));

            if (batchCreateBtn) {
                batchCreateBtn.addEventListener('click', function() {
                    updateBatchPreview();
                    batchCreateModal.show();
                });
            }

            // 清理已禁用兑换码按钮
            const cleanupDisabledBtn = document.getElementById('cleanupDisabledBtn');
            if (cleanupDisabledBtn) {
                cleanupDisabledBtn.addEventListener('click', function() {
                    cleanupDisabledCodes();
                });
            }

            // 批量创建预览更新
            const batchInputs = ['batchCodeType', 'batchCodeCount', 'batchCodePoints', 'batchCodeExpireDays'];
            batchInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updateBatchPreview);
                    element.addEventListener('change', updateBatchPreview);
                }
            });

            // 确认创建单个兑换码
            const confirmCreateCodeBtn = document.getElementById('confirmCreateCodeBtn');
            if (confirmCreateCodeBtn) {
                confirmCreateCodeBtn.addEventListener('click', function() {
                    const codeType = document.getElementById('codeType').value;
                    const points = parseInt(document.getElementById('codePoints').value);
                    const expireDays = parseInt(document.getElementById('codeExpireDays').value);
                    const description = document.getElementById('codeDescription').value.trim();

                    if (!points || points <= 0 || !expireDays || expireDays <= 0) {
                        alert('请填写正确的积分数量和有效期');
                        return;
                    }

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            count: 1,
                            points: points,
                            expire_days: expireDays,
                            description: description,
                            type: codeType
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`成功创建兑换码: ${data.codes[0]}`);
                            createCodeModal.hide();
                            // 清空表单
                            document.getElementById('codeType').value = 'one_time';
                            document.getElementById('codePoints').value = '10';
                            document.getElementById('codeExpireDays').value = '30';
                            document.getElementById('codeDescription').value = '';
                            // 重新加载数据
                            window.loadRedemptionData();
                        } else {
                            alert('创建失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('创建失败: ' + error);
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.innerHTML = '创建';
                    });
                });
            }

            // 确认批量创建兑换码
            const confirmBatchCreateBtn = document.getElementById('confirmBatchCreateBtn');
            if (confirmBatchCreateBtn) {
                confirmBatchCreateBtn.addEventListener('click', function() {
                    const batchCodeType = document.getElementById('batchCodeType').value;
                    const count = parseInt(document.getElementById('batchCodeCount').value);
                    const points = parseInt(document.getElementById('batchCodePoints').value);
                    const expireDays = parseInt(document.getElementById('batchCodeExpireDays').value);
                    const description = document.getElementById('batchCodeDescription').value.trim();

                    if (!count || count <= 0 || count > 100 || !points || points <= 0 || !expireDays || expireDays <= 0) {
                        alert('请填写正确的参数（数量1-100，积分>0，有效期>0）');
                        return;
                    }

                    this.disabled = true;
                    this.textContent = '生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            count: count,
                            points: points,
                            expire_days: expireDays,
                            description: description,
                            type: batchCodeType
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`成功创建 ${data.codes.length} 个兑换码`);
                            batchCreateModal.hide();
                            // 清空表单
                            document.getElementById('batchCodeType').value = 'one_time';
                            document.getElementById('batchCodeCount').value = '10';
                            document.getElementById('batchCodePoints').value = '10';
                            document.getElementById('batchCodeExpireDays').value = '30';
                            document.getElementById('batchCodeDescription').value = '';
                            // 重新加载数据
                            window.loadRedemptionData();
                        } else {
                            alert('创建失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('创建失败: ' + error);
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.textContent = '批量创建';
                    });
                });
            }
        }

        // 更新批量创建预览
        function updateBatchPreview() {
            const codeType = document.getElementById('batchCodeType').value;
            const count = parseInt(document.getElementById('batchCodeCount').value) || 0;
            const points = parseInt(document.getElementById('batchCodePoints').value) || 0;
            const expireDays = parseInt(document.getElementById('batchCodeExpireDays').value) || 0;

            // 更新类型显示
            const typeText = codeType === 'one_time' ? '一次性兑换码' : '活动兑换码';
            document.getElementById('previewType').textContent = typeText;

            document.getElementById('previewCount').textContent = count;
            document.getElementById('previewPoints').textContent = points;
            document.getElementById('previewTotalPoints').textContent = points * count;

            if (expireDays > 0) {
                const expireDate = new Date();
                expireDate.setDate(expireDate.getDate() + expireDays);
                document.getElementById('previewExpireDate').textContent = expireDate.toLocaleDateString();
            } else {
                document.getElementById('previewExpireDate').textContent = '-';
            }
        }

        // 兑换码操作函数
        function toggleRedemptionCode(code, action) {
            if (!confirm(`确定要${action === 'activate' ? '启用' : '禁用'}这个兑换码吗？`)) {
                return;
            }

            fetch('/admin/toggle_redemption_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: code,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (data.success) {
                    window.loadRedemptionData();
                }
            })
            .catch(error => {
                alert('操作失败: ' + error);
            });
        }

        function deleteRedemptionCode(code) {
            if (!confirm(`确定要删除兑换码 ${code} 吗？此操作不可恢复！`)) {
                return;
            }

            fetch('/admin/delete_redemption_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (data.success) {
                    window.loadRedemptionData();
                }
            })
            .catch(error => {
                alert('删除失败: ' + error);
            });
        }

        function cleanupDisabledCodes() {
            if (!confirm('确定要清理所有已禁用的兑换码吗？此操作不可恢复！\n\n已禁用的兑换码将被永久删除。')) {
                return;
            }

            const cleanupBtn = document.getElementById('cleanupDisabledBtn');
            const originalText = cleanupBtn.innerHTML;
            cleanupBtn.disabled = true;
            cleanupBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>清理中...';

            fetch('/admin/cleanup_disabled_codes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.cleaned_count > 0) {
                        alert(`清理完成！\n\n成功删除了 ${data.cleaned_count} 个已禁用的兑换码。`);
                    } else {
                        alert('没有找到需要清理的已禁用兑换码。');
                    }
                    // 重新加载兑换码数据
                    window.loadRedemptionData();
                } else {
                    alert('清理失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('清理失败: ' + error);
            })
            .finally(() => {
                cleanupBtn.disabled = false;
                cleanupBtn.innerHTML = originalText;
            });
        }

        function copyCodeToClipboard(code) {
            navigator.clipboard.writeText(code).then(function() {
                alert('兑换码已复制到剪贴板');
            }, function() {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('兑换码已复制到剪贴板');
            });
        }

        // 批量操作函数
        function selectAllCodes() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            document.getElementById('selectAllCheckbox').checked = true;
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('selectAllCheckbox').checked = false;
        }

        function toggleAllCodes(masterCheckbox) {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(cb => cb.checked = masterCheckbox.checked);
        }

        function getSelectedCodes() {
            const checkboxes = document.querySelectorAll('.code-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function batchActivateCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要启用选中的 ${codes.length} 个兑换码吗？`)) {
                batchOperationCodes(codes, 'activate');
            }
        }

        function batchDeactivateCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要禁用选中的 ${codes.length} 个兑换码吗？`)) {
                batchOperationCodes(codes, 'deactivate');
            }
        }

        function batchDeleteCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要操作的兑换码');
                return;
            }
            if (confirm(`确定要删除选中的 ${codes.length} 个兑换码吗？此操作不可恢复！`)) {
                batchOperationCodes(codes, 'delete');
            }
        }

        function batchOperationCodes(codes, action) {
            const promises = codes.map(code => {
                let url, method, body;

                if (action === 'delete') {
                    url = '/admin/delete_redemption_code';
                    method = 'POST';
                    body = JSON.stringify({ code: code });
                } else {
                    url = '/admin/toggle_redemption_code';
                    method = 'POST';
                    body = JSON.stringify({ code: code, action: action });
                }

                return fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: body
                });
            });

            Promise.all(promises)
                .then(responses => Promise.all(responses.map(r => r.json())))
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;

                    if (failCount === 0) {
                        alert(`批量操作完成，成功处理 ${successCount} 个兑换码`);
                    } else {
                        alert(`批量操作完成，成功 ${successCount} 个，失败 ${failCount} 个`);
                    }

                    window.loadRedemptionData();
                    clearSelection();
                })
                .catch(error => {
                    alert('批量操作失败: ' + error);
                });
        }

        // 批量复制兑换码
        function batchCopyCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要复制的兑换码');
                return;
            }

            // 将兑换码用换行符连接
            const codesText = codes.join('\n');

            // 尝试使用现代剪贴板API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(codesText).then(function() {
                    alert(`已复制 ${codes.length} 个兑换码到剪贴板`);
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyToClipboard(codesText, codes.length);
                });
            } else {
                // 备用方法
                fallbackCopyToClipboard(codesText, codes.length);
            }
        }

        // 备用复制方法
        function fallbackCopyToClipboard(text, count) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert(`已复制 ${count} 个兑换码到剪贴板`);
                } else {
                    alert('复制失败，请手动复制');
                }
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }

            document.body.removeChild(textArea);
        }

        // 批量导出兑换码
        function batchExportCodes() {
            const codes = getSelectedCodes();
            if (codes.length === 0) {
                alert('请先选择要导出的兑换码');
                return;
            }

            // 获取选中兑换码的详细信息
            fetch('/admin/export_redemption_codes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    codes: codes
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('导出请求失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 创建并下载文件
                    downloadCodesAsFile(data.export_data, codes.length);
                } else {
                    alert('导出失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                alert('导出失败: ' + error.message);
            });
        }

        // 下载兑换码文件
        function downloadCodesAsFile(exportData, count) {
            // 创建CSV格式的内容
            let csvContent = '兑换码,积分,类型,状态,创建时间,过期时间,描述,使用次数\n';

            exportData.forEach(item => {
                const status = item.is_expired ? '已过期' : (item.is_active ? '有效' : '已禁用');
                const typeText = item.type === 'one_time' ? '一次性' : '活动码';
                const createDate = new Date(item.created_at).toLocaleDateString();
                const expireDate = new Date(item.expire_at).toLocaleDateString();
                const description = (item.description || '无描述').replace(/"/g, '""'); // 转义双引号

                csvContent += `"${item.code}",${item.points},"${typeText}","${status}","${createDate}","${expireDate}","${description}",${item.used_count}\n`;
            });

            // 创建Blob对象
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });

            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);

            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            link.setAttribute('download', `兑换码导出_${timestamp}.csv`);

            // 触发下载
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理URL对象
            URL.revokeObjectURL(url);

            alert(`成功导出 ${count} 个兑换码到CSV文件`);
        }

        // ==================== 历史记录管理功能 ====================

        function loadHistoryStatistics() {
            fetch('/admin/history_statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.statistics;
                        document.getElementById('totalUsers').textContent = stats.total_users;
                        document.getElementById('usersWithHistory').textContent = stats.users_with_history;
                        document.getElementById('totalRecords').textContent = stats.total_records;
                        document.getElementById('avgRecords').textContent = stats.avg_records_per_user;
                        document.getElementById('records7Days').textContent = stats.records_last_7_days;
                        document.getElementById('records30Days').textContent = stats.records_last_30_days;
                        document.getElementById('maxRecordsPerUser').textContent = stats.max_records_per_user;
                    } else {
                        alert('获取统计信息失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取统计信息失败');
                });
        }

        function previewCleanup() {
            const maxRecords = parseInt(document.getElementById('maxRecords').value);
            const daysToKeep = parseInt(document.getElementById('daysToKeep').value);

            if (maxRecords < 1 || maxRecords > 1000) {
                alert('最大记录数必须在1-1000之间');
                return;
            }

            if (daysToKeep < 1 || daysToKeep > 365) {
                alert('保留天数必须在1-365之间');
                return;
            }

            fetch('/admin/cleanup_history', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    max_records: maxRecords,
                    days_to_keep: daysToKeep,
                    dry_run: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.statistics;
                    const resultDiv = document.getElementById('cleanupResult');
                    const statsDiv = document.getElementById('cleanupStats');

                    statsDiv.innerHTML = `
                        <p><strong>预览结果（模拟运行）：</strong></p>
                        <ul>
                            <li>将影响用户数：${stats.cleaned_users}</li>
                            <li>当前总记录数：${stats.total_records_before}</li>
                            <li>清理后记录数：${stats.total_records_after}</li>
                            <li>将删除记录数：${stats.records_removed}</li>
                            <li>数据减少：${stats.records_removed > 0 ? ((stats.records_removed / stats.total_records_before) * 100).toFixed(1) : 0}%</li>
                        </ul>
                    `;

                    resultDiv.style.display = 'block';
                    resultDiv.className = 'mt-3 alert alert-info';
                } else {
                    alert('预览失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('预览失败');
            });
        }

        function cleanupHistory() {
            const maxRecords = parseInt(document.getElementById('maxRecords').value);
            const daysToKeep = parseInt(document.getElementById('daysToKeep').value);
            const dryRun = document.getElementById('dryRun').checked;

            if (maxRecords < 1 || maxRecords > 1000) {
                alert('最大记录数必须在1-1000之间');
                return;
            }

            if (daysToKeep < 1 || daysToKeep > 365) {
                alert('保留天数必须在1-365之间');
                return;
            }

            if (!dryRun && !confirm('确定要执行历史记录清理吗？此操作不可撤销！')) {
                return;
            }

            fetch('/admin/cleanup_history', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    max_records: maxRecords,
                    days_to_keep: daysToKeep,
                    dry_run: dryRun
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.statistics;
                    const resultDiv = document.getElementById('cleanupResult');
                    const statsDiv = document.getElementById('cleanupStats');

                    if (data.dry_run) {
                        statsDiv.innerHTML = `
                            <p><strong>模拟运行结果：</strong></p>
                            <ul>
                                <li>将影响用户数：${stats.cleaned_users}</li>
                                <li>当前总记录数：${stats.total_records_before}</li>
                                <li>清理后记录数：${stats.total_records_after}</li>
                                <li>将删除记录数：${stats.records_removed}</li>
                                <li>数据减少：${stats.records_removed > 0 ? ((stats.records_removed / stats.total_records_before) * 100).toFixed(1) : 0}%</li>
                            </ul>
                        `;
                        resultDiv.className = 'mt-3 alert alert-info';
                    } else {
                        statsDiv.innerHTML = `
                            <p><strong>清理完成！</strong></p>
                            <ul>
                                <li>影响用户数：${stats.cleaned_users}</li>
                                <li>清理前总记录数：${stats.total_records_before}</li>
                                <li>清理后总记录数：${stats.total_records_after}</li>
                                <li>删除记录数：${stats.records_removed}</li>
                                <li>数据减少：${stats.records_removed > 0 ? ((stats.records_removed / stats.total_records_before) * 100).toFixed(1) : 0}%</li>
                            </ul>
                        `;
                        resultDiv.className = 'mt-3 alert alert-success';

                        // 清理完成后刷新统计信息
                        loadHistoryStatistics();
                    }

                    resultDiv.style.display = 'block';
                } else {
                    alert('清理失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('清理失败');
            });
        }

        // ==================== 申请管理相关函数 ====================

        // 加载申请数据
        function loadApplicationsData() {
            // 加载申请统计
            fetch('/admin/applications/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.statistics;
                        document.getElementById('pendingApplicationsCount').textContent = stats.pending_applications || 0;
                        document.getElementById('approvedApplicationsCount').textContent = stats.approved_applications || 0;
                        document.getElementById('rejectedApplicationsCount').textContent = stats.rejected_applications || 0;
                        document.getElementById('totalApplicationsCount').textContent = stats.total_applications || 0;
                    }
                })
                .catch(error => {
                    console.error('加载申请统计失败:', error);
                });

            // 加载申请列表
            loadApplicationsList();
        }

        // 加载申请列表
        function loadApplicationsList(filters = {}) {
            const params = new URLSearchParams();
            if (filters.type) params.append('type', filters.type);
            if (filters.status) params.append('status', filters.status);
            if (filters.username) params.append('username', filters.username);

            fetch(`/admin/applications?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayApplicationsTable(data.applications);
                    } else {
                        document.getElementById('applicationsTable').innerHTML =
                            `<div class="alert alert-danger">加载申请数据失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载申请列表失败:', error);
                    document.getElementById('applicationsTable').innerHTML =
                        `<div class="alert alert-danger">加载申请数据失败: ${error}</div>`;
                });
        }

        // 显示申请列表
        function displayApplicationsTable(applications) {
            const container = document.getElementById('applicationsTable');

            if (applications.length === 0) {
                container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无申请记录</p></div>';
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>类型</th>
                                <th>内容</th>
                                <th>状态</th>
                                <th>提交时间</th>
                                <th>处理时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            applications.forEach(app => {
                const submitTime = new Date(app.submit_time).toLocaleString();
                const processTime = app.process_time ? new Date(app.process_time).toLocaleString() : '-';

                let statusBadge, typeInfo, contentInfo;

                // 状态标签
                if (app.status === 'pending') {
                    statusBadge = '<span class="badge bg-warning">待处理</span>';
                } else if (app.status === 'approved') {
                    statusBadge = '<span class="badge bg-success">已通过</span>';
                } else {
                    statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                }

                // 类型和内容信息
                if (app.type === 'points') {
                    typeInfo = '<span class="badge bg-primary">点数申请</span>';
                    contentInfo = `
                        <div>
                            <strong>${app.points_amount} 点数</strong><br>
                            <small class="text-muted">${app.reason.length > 30 ? app.reason.substring(0, 30) + '...' : app.reason}</small>
                        </div>
                    `;
                } else {
                    typeInfo = '<span class="badge bg-info">反馈建议</span>';
                    const feedbackTypes = {
                        'bug': 'Bug报告',
                        'feature': '功能建议',
                        'improvement': '改进建议',
                        'question': '使用问题',
                        'other': '其他'
                    };
                    contentInfo = `
                        <div>
                            <strong>${app.title}</strong><br>
                            <small class="text-muted">${feedbackTypes[app.feedback_type] || app.feedback_type}</small>
                        </div>
                    `;
                }

                html += `
                    <tr>
                        <td>${app.id}</td>
                        <td>
                            <div>
                                <strong>${app.username}</strong><br>
                                <small class="text-muted">积分: ${app.user_info.current_points}</small>
                            </div>
                        </td>
                        <td>${typeInfo}</td>
                        <td>${contentInfo}</td>
                        <td>${statusBadge}</td>
                        <td><small>${submitTime}</small></td>
                        <td><small>${processTime}</small></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="viewApplicationDetails(${app.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="sendMessageToApplicant('${app.username}', ${app.id}, '${app.type}')" title="发送站内信">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                ${app.status === 'pending' ? `
                                    <button class="btn btn-outline-success" onclick="processApplication(${app.id}, 'approved')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="processApplication(${app.id}, 'rejected')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 筛选申请
        function filterApplications() {
            const filters = {
                type: document.getElementById('applicationTypeFilter').value,
                status: document.getElementById('applicationStatusFilter').value,
                username: document.getElementById('applicationUserFilter').value.trim()
            };
            loadApplicationsList(filters);
        }

        // 查看申请详情
        function viewApplicationDetails(applicationId) {
            fetch(`/admin/applications/${applicationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showApplicationModal(data.application);
                    } else {
                        alert('获取申请详情失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取申请详情失败:', error);
                    alert('获取申请详情失败');
                });
        }

        // 显示申请详情模态框
        function showApplicationModal(app) {
            let modalContent = `
                <div class="modal fade" id="applicationModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">申请详情 #${app.id}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>基本信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>申请ID:</td><td>${app.id}</td></tr>
                                            <tr><td>用户名:</td><td>${app.username}</td></tr>
                                            <tr><td>申请类型:</td><td>${app.type === 'points' ? '点数申请' : '反馈建议'}</td></tr>
                                            <tr><td>状态:</td><td>${app.status === 'pending' ? '待处理' : (app.status === 'approved' ? '已通过' : '已拒绝')}</td></tr>
                                            <tr><td>提交时间:</td><td>${new Date(app.submit_time).toLocaleString()}</td></tr>
                                            ${app.process_time ? `<tr><td>处理时间:</td><td>${new Date(app.process_time).toLocaleString()}</td></tr>` : ''}
                                            ${app.processed_by ? `<tr><td>处理人:</td><td>${app.processed_by}</td></tr>` : ''}
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>用户信息</h6>
                                        <table class="table table-sm">
                                            <tr><td>当前积分:</td><td>${app.user_info.current_points}</td></tr>
                                            <tr><td>签到次数:</td><td>${app.user_info.total_checkins}</td></tr>
                                            <tr><td>注册时间:</td><td>${app.user_info.registration_date || '未知'}</td></tr>
                                        </table>
                                    </div>
                                </div>
            `;

            if (app.type === 'points') {
                modalContent += `
                    <div class="mt-3">
                        <h6>申请内容</h6>
                        <p><strong>申请点数:</strong> ${app.points_amount}</p>
                        <p><strong>申请理由:</strong></p>
                        <div class="border p-3 bg-light">${app.reason}</div>
                    </div>
                `;
            } else {
                const feedbackTypes = {
                    'bug': 'Bug报告',
                    'feature': '功能建议',
                    'improvement': '改进建议',
                    'question': '使用问题',
                    'other': '其他'
                };
                modalContent += `
                    <div class="mt-3">
                        <h6>反馈内容</h6>
                        <p><strong>反馈类型:</strong> ${feedbackTypes[app.feedback_type] || app.feedback_type}</p>
                        <p><strong>标题:</strong> ${app.title}</p>
                        <p><strong>详细内容:</strong></p>
                        <div class="border p-3 bg-light">${app.content}</div>
                    </div>
                `;
            }

            if (app.admin_note) {
                modalContent += `
                    <div class="mt-3">
                        <h6>管理员备注</h6>
                        <div class="border p-3 bg-warning bg-opacity-10">${app.admin_note}</div>
                    </div>
                `;
            }

            modalContent += `
                            </div>
                            <div class="modal-footer">
                                ${app.status === 'pending' ? `
                                    <button type="button" class="btn btn-success" onclick="processApplicationFromModal(${app.id}, 'approved')">
                                        <i class="fas fa-check me-1"></i>通过
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="processApplicationFromModal(${app.id}, 'rejected')">
                                        <i class="fas fa-times me-1"></i>拒绝
                                    </button>
                                ` : ''}
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('applicationModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            modal.show();
        }

        // 处理申请
        function processApplication(applicationId, action) {
            const note = prompt(action === 'approved' ? '请输入通过理由（可选）:' : '请输入拒绝理由（可选）:');
            if (note === null) return; // 用户取消

            fetch(`/admin/applications/${applicationId}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    note: note || ''
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    loadApplicationsData(); // 重新加载数据
                } else {
                    alert('处理失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('处理申请失败:', error);
                alert('处理失败');
            });
        }

        // 从模态框处理申请
        function processApplicationFromModal(applicationId, action) {
            processApplication(applicationId, action);
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('applicationModal'));
            if (modal) {
                modal.hide();
            }
        }

        // 向申请者发送站内信
        function sendMessageToApplicant(username, applicationId, applicationType) {
            // 填充模态框数据
            document.getElementById('applicationMessageRecipient').value = username;
            document.getElementById('applicationId').value = applicationId;
            document.getElementById('applicationType').value = applicationType;

            // 根据申请类型设置默认标题
            const defaultTitle = applicationType === 'points' ? '关于您的点数申请' : '关于您的反馈建议';
            document.getElementById('applicationMessageTitle').value = defaultTitle;

            // 清空内容
            document.getElementById('applicationMessageContent').value = '';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('sendMessageFromApplicationModal'));
            modal.show();
        }

        // 当切换到申请管理标签页时自动加载数据
        document.addEventListener('shown.bs.tab', function (event) {
            if (event.target.getAttribute('href') === '#applicationsTab') {
                loadApplicationsData();
            }
        });

        // 刷新申请数据按钮
        document.addEventListener('DOMContentLoaded', function() {
            const refreshBtn = document.getElementById('refreshApplicationsBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', loadApplicationsData);
            }

            // 绑定发送申请消息按钮事件
            const sendApplicationMessageBtn = document.getElementById('sendApplicationMessageBtn');
            if (sendApplicationMessageBtn) {
                sendApplicationMessageBtn.addEventListener('click', function() {
                    const recipient = document.getElementById('applicationMessageRecipient').value;
                    const title = document.getElementById('applicationMessageTitle').value.trim();
                    const content = document.getElementById('applicationMessageContent').value.trim();

                    if (!title || !content) {
                        alert('请填写消息标题和内容');
                        return;
                    }

                    // 禁用按钮，防止重复提交
                    const originalText = sendApplicationMessageBtn.innerHTML;
                    sendApplicationMessageBtn.disabled = true;
                    sendApplicationMessageBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>发送中...';

                    // 发送消息
                    sendMessage(recipient, title, content)
                        .then(data => {
                            if (data.success) {
                                alert('站内信发送成功！');
                                // 关闭模态框
                                const modal = bootstrap.Modal.getInstance(document.getElementById('sendMessageFromApplicationModal'));
                                if (modal) {
                                    modal.hide();
                                }
                                // 清空表单
                                document.getElementById('applicationMessageTitle').value = '';
                                document.getElementById('applicationMessageContent').value = '';
                            } else {
                                alert('发送失败：' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('发送站内信出错:', error);
                            alert('发送失败，请稍后重试');
                        })
                        .finally(() => {
                            // 恢复按钮
                            sendApplicationMessageBtn.disabled = false;
                            sendApplicationMessageBtn.innerHTML = originalText;
                        });
                });
            }
        });

        // 当切换到历史记录标签页时自动加载统计信息
        document.addEventListener('shown.bs.tab', function (event) {
            if (event.target.getAttribute('href') === '#historyTab') {
                loadHistoryStatistics();
            }
        });

        // ==================== 授权用户管理相关函数 ====================

        // 加载授权用户数据
        function loadAuthorizedUsersData() {
            // 加载授权用户列表
            fetch('/admin/authorized_users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayAuthorizedUsersTable(data.authorized_users);
                        updateAuthorizedUsersStats(data.authorized_users);
                    } else {
                        document.getElementById('authorizedUsersTable').innerHTML = `<div class="alert alert-danger">加载授权用户失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('authorizedUsersTable').innerHTML = `<div class="alert alert-danger">加载授权用户失败: ${error}</div>`;
                });
        }

        // 显示授权用户表格
        function displayAuthorizedUsersTable(authorizedUsers) {
            if (authorizedUsers.length === 0) {
                document.getElementById('authorizedUsersTable').innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无授权用户</p></div>';
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>积分</th>
                                <th>注册时间</th>
                                <th>授权时间</th>
                                <th>授权人</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            authorizedUsers.forEach(user => {
                const currentUsername = getCurrentUsername();
                const isCurrentUser = user.username === currentUsername;
                const isAdmin = user.is_admin;

                html += `
                    <tr>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email || '-'}</td>
                        <td>${user.points}</td>
                        <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                        <td>${user.authorized_at ? new Date(user.authorized_at).toLocaleDateString() : '-'}</td>
                        <td>${user.authorized_by || '-'}</td>
                        <td>${isAdmin ? '<span class="badge bg-danger">管理员</span>' : '<span class="badge bg-success">授权用户</span>'}</td>
                        <td>
                            ${!isCurrentUser && !isAdmin ? `
                                <button type="button" class="btn btn-outline-danger btn-sm"
                                        onclick="setUserAuthorized('${user.username}', false)"
                                        title="取消授权">
                                    <i class="fas fa-user-times me-1"></i>取消授权
                                </button>
                            ` : ''}
                            ${isCurrentUser ? '<span class="text-muted small">当前用户</span>' : ''}
                            ${isAdmin && !isCurrentUser ? '<span class="text-info small">管理员</span>' : ''}
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('authorizedUsersTable').innerHTML = html;
        }

        // 更新授权用户统计
        function updateAuthorizedUsersStats(authorizedUsers) {
            // 从用户列表API获取所有用户数据来计算统计
            fetch('/admin/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const allUsers = data.users;
                        const totalUsers = allUsers.length;
                        const authorizedCount = authorizedUsers.length;
                        const adminCount = allUsers.filter(user => user.is_admin).length;
                        const regularCount = totalUsers - authorizedCount - adminCount;
                        const authorizationRate = totalUsers > 0 ? Math.round((authorizedCount / totalUsers) * 100) : 0;

                        document.getElementById('totalAuthorizedUsers').textContent = authorizedCount;
                        document.getElementById('totalRegularUsers').textContent = regularCount;
                        document.getElementById('totalAdminUsers').textContent = adminCount;
                        document.getElementById('authorizationRate').textContent = authorizationRate + '%';
                    }
                })
                .catch(error => {
                    console.error('获取用户统计失败:', error);
                });
        }

        // 设置用户授权状态
        function setUserAuthorized(username, authorized) {
            const action = authorized ? '设为授权用户' : '取消授权';
            if (!confirm(`确定要${action} "${username}" 吗？`)) {
                return;
            }

            fetch('/admin/set_user_authorized', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    authorized: authorized
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (data.success) {
                    // 重新加载用户数据和授权用户数据
                    loadUsersData();
                    loadAuthorizedUsersData();
                }
            })
            .catch(error => {
                alert('操作失败: ' + error);
            });
        }



        // 刷新授权用户数据
        document.getElementById('refreshAuthorizedUsersBtn')?.addEventListener('click', function() {
            loadAuthorizedUsersData();
        });

        // 当切换到授权用户标签页时自动加载数据
        document.addEventListener('shown.bs.tab', function (event) {
            if (event.target.getAttribute('href') === '#authorizedUsersTab') {
                loadAuthorizedUsersData();
            }
        });

        // ==================== 站内信管理相关函数 ====================

        // 加载站内信统计数据
        function loadMessageStatistics() {
            fetch('/admin/messages/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.statistics;
                        document.getElementById('totalMessagesCount').textContent = stats.total_messages || 0;
                        document.getElementById('readMessagesCount').textContent = stats.total_read_messages || 0;
                        document.getElementById('unreadMessagesCount').textContent = stats.total_unread_messages || 0;
                        document.getElementById('adminMessagesCount').textContent = stats.total_admin_messages || 0;
                    } else {
                        console.error('加载站内信统计失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('加载站内信统计出错:', error);
                });
        }

        // 发送站内信
        function sendMessage(recipient, title, content) {
            return fetch('/admin/messages/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient: recipient,
                    title: title,
                    content: content
                })
            })
            .then(response => response.json());
        }

        // 显示消息提示
        function showMessageAlert(message, type = 'info') {
            const alertDiv = document.getElementById('messageAlert');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.classList.remove('d-none');

            // 3秒后自动隐藏
            setTimeout(() => {
                alertDiv.classList.add('d-none');
            }, 3000);
        }

        // 当切换到站内信管理标签页时自动加载数据
        document.addEventListener('shown.bs.tab', function (event) {
            if (event.target.getAttribute('href') === '#messagesTab') {
                loadMessageStatistics();
            }
        });

        // 站内信管理相关事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 发送给所有用户按钮
            const sendToAllBtn = document.getElementById('sendToAllBtn');
            if (sendToAllBtn) {
                sendToAllBtn.addEventListener('click', function() {
                    document.getElementById('messageRecipient').value = 'all';
                });
            }

            // 清空表单按钮
            const clearMessageFormBtn = document.getElementById('clearMessageFormBtn');
            if (clearMessageFormBtn) {
                clearMessageFormBtn.addEventListener('click', function() {
                    document.getElementById('messageRecipient').value = '';
                    document.getElementById('messageTitle').value = '';
                    document.getElementById('messageContent').value = '';
                });
            }

            // 刷新统计按钮
            const refreshMessageStatsBtn = document.getElementById('refreshMessageStatsBtn');
            if (refreshMessageStatsBtn) {
                refreshMessageStatsBtn.addEventListener('click', loadMessageStatistics);
            }

            // 发送消息表单提交
            const sendMessageForm = document.getElementById('sendMessageForm');
            if (sendMessageForm) {
                sendMessageForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const recipient = document.getElementById('messageRecipient').value.trim();
                    const title = document.getElementById('messageTitle').value.trim();
                    const content = document.getElementById('messageContent').value.trim();

                    if (!recipient || !title || !content) {
                        showMessageAlert('请填写完整的收件人、标题和内容', 'warning');
                        return;
                    }

                    // 禁用提交按钮，防止重复提交
                    const submitBtn = sendMessageForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>发送中...';

                    sendMessage(recipient, title, content)
                        .then(data => {
                            if (data.success) {
                                showMessageAlert('站内信发送成功！', 'success');
                                // 清空表单
                                document.getElementById('messageRecipient').value = '';
                                document.getElementById('messageTitle').value = '';
                                document.getElementById('messageContent').value = '';
                                // 刷新统计
                                loadMessageStatistics();
                            } else {
                                showMessageAlert('发送失败：' + data.message, 'danger');
                            }
                        })
                        .catch(error => {
                            console.error('发送站内信出错:', error);
                            showMessageAlert('发送失败，请稍后重试', 'danger');
                        })
                        .finally(() => {
                            // 恢复提交按钮
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                        });
                });
            }
        });
    </script>

    <!-- 从申请管理发送站内信的模态框 -->
    <div class="modal fade" id="sendMessageFromApplicationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发送站内信</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="sendMessageFromApplicationForm">
                        <div class="mb-3">
                            <label for="applicationMessageRecipient" class="form-label">收件人</label>
                            <input type="text" class="form-control" id="applicationMessageRecipient" readonly>
                            <div class="form-text">将发送给申请者</div>
                        </div>
                        <div class="mb-3">
                            <label for="applicationMessageTitle" class="form-label">消息标题</label>
                            <input type="text" class="form-control" id="applicationMessageTitle" placeholder="请输入消息标题" required>
                        </div>
                        <div class="mb-3">
                            <label for="applicationMessageContent" class="form-label">消息内容</label>
                            <textarea class="form-control" id="applicationMessageContent" rows="5" placeholder="请输入消息内容" required></textarea>
                        </div>
                        <input type="hidden" id="applicationId" value="">
                        <input type="hidden" id="applicationType" value="">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="sendApplicationMessageBtn">发送消息</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
